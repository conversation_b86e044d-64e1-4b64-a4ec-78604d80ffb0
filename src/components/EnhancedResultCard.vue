<template>
  <div class="enhanced-result-card">
    <div class="result-header">
      <h3>{{ $t('results.title') }}</h3>
      <div class="header-actions">
        <button @click="toggleAnalysis" class="analysis-toggle">
          {{ showAnalysis ? $t('analysis.hideAnalysis') : $t('analysis.showAnalysis') }}
        </button>
        <button @click="handleRegenerate" class="regenerate-button">
          {{ $t('results.regenerate') }}
        </button>
      </div>
    </div>

    <div class="results-list">
      <div
        v-for="(result, index) in analyzedResults"
        :key="index"
        class="result-item"
        :class="{ 'expanded': showAnalysis }"
      >
        <div class="result-content">
          <div class="bundle-info">
            <code class="bundle-id">{{ result.bundleId }}</code>
            <div v-if="showAnalysis" class="quality-badge" :class="getGradeClass(result.grade)">
              {{ result.grade }}
            </div>
          </div>

          <div class="result-actions">
            <button
              @click="copyToClipboard(result.bundleId, index)"
              class="copy-button"
              :class="{ 'copied': copiedIndex === index }"
            >
              {{ copiedIndex === index ? $t('results.copied') : $t('results.copy') }}
            </button>
          </div>
        </div>

        <!-- 质量分析面板 -->
        <div v-if="showAnalysis" class="analysis-panel">
          <div class="score-overview">
            <div class="total-score">
              <span class="score-label">{{ $t('analysis.totalScore') }}</span>
              <span class="score-value" :class="getScoreClass(result.totalScore)">
                {{ result.totalScore }}
              </span>
            </div>

            <div class="score-breakdown">
              <div v-for="(score, key) in result.scores" :key="key" class="score-item">
                <span class="score-name">{{ getScoreName(key) }}</span>
                <div class="score-bar">
                  <div
                    class="score-fill"
                    :style="{ width: score + '%' }"
                    :class="getScoreClass(score)"
                  ></div>
                </div>
                <span class="score-number">{{ Math.round(score) }}</span>
              </div>
            </div>
          </div>

          <!-- 改进建议 -->
          <div v-if="result.suggestions.length > 0" class="suggestions">
            <h4>{{ $t('analysis.suggestions') }}</h4>
            <div class="suggestion-list">
              <div
                v-for="(suggestion, idx) in result.suggestions"
                :key="idx"
                class="suggestion-item"
                :class="'priority-' + suggestion.priority"
              >
                <span class="suggestion-icon">💡</span>
                <span class="suggestion-text">{{ suggestion.message }}</span>
              </div>
            </div>
          </div>

          <!-- 技术分析 -->
          <div class="technical-analysis">
            <h4>{{ $t('analysis.technicalAnalysis') }}</h4>
            <div class="analysis-grid">
              <div class="analysis-item">
                <span class="label">{{ $t('analysis.length') }}:</span>
                <span class="value">{{ result.analysis.length }} {{ $t('analysis.characters') }}</span>
              </div>
              <div class="analysis-item">
                <span class="label">{{ $t('analysis.components') }}:</span>
                <span class="value">{{ result.analysis.parts }}</span>
              </div>
              <div class="analysis-item">
                <span class="label">{{ $t('analysis.topLevelDomain') }}:</span>
                <span class="value">{{ result.analysis.tld }}</span>
              </div>
              <div class="analysis-item">
                <span class="label">{{ $t('analysis.hasNumbers') }}:</span>
                <span class="value">{{ result.analysis.hasNumbers ? $t('analysis.yes') : $t('analysis.no') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import useBundleIdAnalyzer from '@/composables/useBundleIdAnalyzer'

const { t } = useI18n()
const { analyzeMultiple } = useBundleIdAnalyzer()

const props = defineProps({
  results: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['regenerate'])

const copiedIndex = ref(-1)
const showAnalysis = ref(false)

// 分析结果
const analyzedResults = computed(() => {
  return analyzeMultiple(props.results)
})

const copyToClipboard = async (text, index) => {
  try {
    await navigator.clipboard.writeText(text)
    copiedIndex.value = index
    setTimeout(() => {
      copiedIndex.value = -1
    }, 2000)
  } catch (err) {
    console.error('Failed to copy text: ', err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)

    copiedIndex.value = index
    setTimeout(() => {
      copiedIndex.value = -1
    }, 2000)
  }
}

const handleRegenerate = () => {
  emit('regenerate')
}

const toggleAnalysis = () => {
  showAnalysis.value = !showAnalysis.value
}

const getGradeClass = (grade) => {
  if (grade.startsWith('A')) return 'grade-a'
  if (grade.startsWith('B')) return 'grade-b'
  if (grade.startsWith('C')) return 'grade-c'
  if (grade.startsWith('D')) return 'grade-d'
  return 'grade-f'
}

const getScoreClass = (score) => {
  if (score >= 80) return 'score-excellent'
  if (score >= 70) return 'score-good'
  if (score >= 60) return 'score-fair'
  return 'score-poor'
}

const getScoreName = (key) => {
  return t(`analysis.${key}`)
}
</script>

<style scoped>
.enhanced-result-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  padding: 1.5rem;
  margin-top: 2rem;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
}

.result-header h3 {
  color: #f8fafc;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.analysis-toggle {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.analysis-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.regenerate-button {
  background: linear-gradient(135deg, #10b981 0%, #06d6a0 100%);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.regenerate-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-item {
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  background: rgba(51, 65, 85, 0.3);
}

.result-item:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.result-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.4);
}

.bundle-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.bundle-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #e2e8f0;
  background: rgba(15, 23, 42, 0.8);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  word-break: break-all;
}

.quality-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 700;
  text-align: center;
  min-width: 2rem;
}

.grade-a { background: #10b981; color: white; }
.grade-b { background: #3b82f6; color: white; }
.grade-c { background: #f59e0b; color: white; }
.grade-d { background: #ef4444; color: white; }
.grade-f { background: #6b7280; color: white; }

.copy-button {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.copy-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.copy-button.copied {
  background: linear-gradient(135deg, #10b981 0%, #06d6a0 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.analysis-panel {
  padding: 1.5rem;
  background: rgba(15, 23, 42, 0.6);
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.score-overview {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.total-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 8px;
}

.score-label {
  font-size: 0.875rem;
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.score-value {
  font-size: 2rem;
  font-weight: 700;
  border-radius: 50%;
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.score-item {
  display: grid;
  grid-template-columns: 100px 1fr 40px;
  gap: 0.75rem;
  align-items: center;
  margin-bottom: 0.5rem;
}

.score-name {
  font-size: 0.875rem;
  color: #e2e8f0;
  word-break: break-word;
  line-height: 1.2;
}

.score-bar {
  height: 8px;
  background: rgba(71, 85, 105, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.score-excellent { background: #10b981; }
.score-good { background: #3b82f6; }
.score-fair { background: #f59e0b; }
.score-poor { background: #ef4444; }

.score-number {
  font-size: 0.875rem;
  color: #cbd5e1;
  text-align: right;
}

.suggestions {
  margin-bottom: 1.5rem;
}

.suggestions h4 {
  color: #f8fafc;
  margin: 0 0 1rem 0;
  font-size: 1rem;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
}

.priority-high { background: rgba(239, 68, 68, 0.1); border-left: 3px solid #ef4444; }
.priority-medium { background: rgba(245, 158, 11, 0.1); border-left: 3px solid #f59e0b; }
.priority-low { background: rgba(59, 130, 246, 0.1); border-left: 3px solid #3b82f6; }

.suggestion-text {
  color: #e2e8f0;
}

.technical-analysis h4 {
  color: #f8fafc;
  margin: 0 0 1rem 0;
  font-size: 1rem;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 4px;
  font-size: 0.875rem;
}

.analysis-item .label {
  color: #cbd5e1;
}

.analysis-item .value {
  color: #f8fafc;
  font-weight: 500;
}

@media (max-width: 768px) {
  .enhanced-result-card {
    margin: 1rem;
    padding: 1rem;
  }

  .result-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .result-content {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .bundle-info {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .copy-button {
    width: 100%;
  }

  .score-overview {
    grid-template-columns: 1fr;
  }

  .score-item {
    grid-template-columns: 120px 1fr 40px;
    gap: 0.5rem;
  }

  .analysis-grid {
    grid-template-columns: 1fr;
  }
}
</style>
