export default function useAiGenerator() {
  const generateWithAI = async (input, count = 3) => {
    try {
      const res = await fetch('/api/ai-suggest', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...input, count })
      });
      if (!res.ok) {
        console.warn('AI suggest failed:', await res.text());
        return [];
      }
      const data = await res.json();
      return Array.isArray(data.bundleIds) ? data.bundleIds : [];
    } catch (e) {
      console.warn('AI suggest error:', e);
      return [];
    }
  };

  return { generateWithAI };
}

