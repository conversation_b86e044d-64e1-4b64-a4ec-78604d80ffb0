<template>
  <div class="generator-view">
    <div class="generator-container">
      <Transition name="slide-up" appear>
        <BundleIdForm @submit="handleSubmit" />
      </Transition>

      <Transition name="fade" mode="out-in">
        <EnhancedResultCard
          v-if="results.length"
          ref="resultCardRef"
          :key="results.join(',')"
          :results="results"
          @regenerate="generateIds"
        />
      </Transition>

      <Transition name="slide-up" appear>
        <HistoryList />
      </Transition>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import BundleIdForm from '@/components/BundleIdForm.vue';
import EnhancedResultCard from '@/components/EnhancedResultCard.vue';
import HistoryList from '@/components/HistoryList.vue';
import useAiGenerator from '@/composables/useAiGenerator';

import { useHistoryStore } from '@/stores/history';

const { generateWithAI } = useAiGenerator();

const historyStore = useHistoryStore();
const results = ref([]);
const currentInput = ref(null);
const isRegeneration = ref(false); // Track if this is a regeneration
const resultCardRef = ref(null); // Reference to result card for scrolling

const handleSubmit = (input) => {
  currentInput.value = input;
  isRegeneration.value = false; // This is initial generation
  generateIds();
};

const generateIds = async () => {
  if (!currentInput.value) {
    return;
  }

  // GPT-only generation (server-side)
  let aiResults = [];
  try {
    aiResults = await generateWithAI(currentInput.value, 3);
  } catch {
    aiResults = [];
  }

  results.value = aiResults;

  // Add to history with regeneration flag
  historyStore.addRecord(currentInput.value, aiResults, isRegeneration.value);

  // Set regeneration flag for subsequent calls
  isRegeneration.value = true;

  // Scroll to results with smooth animation
  nextTick(() => {
    if (resultCardRef.value) {
      resultCardRef.value.$el.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  });
};
</script>

<style scoped>
.generator-view {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 2rem 0;
}

.generator-container {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Transition animations */
.slide-up-enter-active {
  transition: all 0.6s ease-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.4s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

@media (max-width: 768px) {
  .generator-view {
    padding: 1rem 0;
  }

  .generator-container {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .generator-view {
    padding: 0.5rem 0;
  }

  .generator-container {
    gap: 1rem;
  }
}
</style>
